'use client';

import React from 'react';
import { Layout, Row, Col } from 'antd';
import Header from './components/Header';
import Hero from './components/Hero';
import Footer from './components/Footer';
import './styles.css';

const { Content } = Layout;

/**
 * 前台首页 - 仿照参考网站的三列布局
 */
export default function FrontPage() {
  return (
    <Layout style={{ minHeight: '100vh', backgroundColor: '#f5f5f5' }}>
      <Header />
      <Hero />
      <div style={{ width: '1140px' }}>
        <img
          src='/images/interval.png'
          style={{
            width: '100%',
            height: '100%',
            objectFit: 'cover',
            display: 'block'
          }}
        />
      </div>
      <Footer />
    </Layout>
  );
}
