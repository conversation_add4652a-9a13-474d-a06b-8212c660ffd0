'use client';

import React from 'react';
import { Layout, Menu, Input, Button, Row, Col, Space } from 'antd';
import {
  SearchOutlined,
  PhoneOutlined,
} from '@ant-design/icons';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

const { Header: AntHeader } = Layout;

/**
 * 前台页面头部组件 - 仿照参考网站设计
 */
export default function Header() {
  const pathname = usePathname();

  // 主导航菜单项
  const mainMenuItems = [
    {
      key: 'home',
      label: <Link href="/front">首页</Link>,
    },
    {
      key: 'quote',
      label: <Link href="/front/quote">包装报价</Link>,
    },
    {
      key: 'products',
      label: <Link href="/front/products">现货专区</Link>,
    },
    {
      key: 'user',
      label: <Link href="/front/user">用户中心</Link>,
    },
    {
      key: 'about',
      label: <Link href="/front/about">关于我们</Link>,
    },
    {
      key: 'news',
      label: <Link href="/front/news">新闻中心</Link>,
    },
  ];

  // 获取当前选中的菜单键
  const getSelectedKeys = () => {
    if (pathname === '/front') return ['home'];
    if (pathname.startsWith('/front/quote')) return ['quote'];
    if (pathname.startsWith('/front/products')) return ['products'];
    if (pathname.startsWith('/front/user')) return ['user'];
    if (pathname.startsWith('/front/about')) return ['about'];
    if (pathname.startsWith('/front/news')) return ['news'];
    return [];
  };

  return (
    <>
      {/* 顶部信息栏 */}
      <div style={{ backgroundColor: '#393D49', borderBottom: '1px solid #e0e0e0', padding: '8px 0' }}>
        <Row justify="space-between" align="middle" style={{ maxWidth: '1200px', margin: '0 auto', padding: '0 20px' }}>
          <Col>
            <Space>
              <PhoneOutlined style={{ color: 'white' }} />
              <span style={{ color: 'white', fontSize: '12px' }}>
                24小时热线：18638728164
              </span>
            </Space>
          </Col>
          <Col>
            <Space size="large">
              <Link href="/front/login">
                <Button type="link" size="small" style={{ padding: 0, height: 'auto', color: 'white' }}>
                  您好，请登录
                </Button>
              </Link>
              <Link href="/front/user/orders">
                <Button type="link" size="small" style={{ padding: 0, height: 'auto', color: 'white' }}>
                  我的订单
                </Button>
              </Link>
              <Link href="/front/cart">
                <Button type="link" size="small" style={{ padding: 0, height: 'auto', color: 'white' }}>
                  购物车
                </Button>
              </Link>
              <Link href="/front/about">
                <Button type="link" size="small" style={{ padding: 0, height: 'auto', color: 'white' }}>
                  关于我们
                </Button>
              </Link>
            </Space>
          </Col>
        </Row>
      </div>

      {/* 主导航栏 */}
      <AntHeader style={{ backgroundColor: '#fff', borderBottom: '1px solid #e0e0e0', padding: '0' }}>
        <Row style={{ maxWidth: '1200px', margin: '0 auto', padding: '0 20px', height: '80px' }}>
          {/* Logo */}
          <Col flex="none">
            <Link href="/front">
              <Button type="link" style={{ padding: 0, height: 'auto' }}>
                <img
                  src="/images/logo.png"
                  alt="艺创包装"
                  style={{ height: '50px', width: 'auto' }}
                />
                <div
                  style={{
                    display: 'none',
                    fontSize: '24px',
                    fontWeight: 'bold',
                    color: '#1890ff'
                  }}
                >
                  艺创包装
                </div>
              </Button>
            </Link>
          </Col>

          {/* 主导航菜单 */}
          <Col flex="auto" style={{ display: 'flex', justifyContent: 'flex-end' }}>
            <Menu
              mode="horizontal"
              selectedKeys={getSelectedKeys()}
              items={mainMenuItems}
              style={{
                border: 'none',
                backgroundColor: 'transparent',
                fontSize: '14px'
              }}
            />
          </Col>
        </Row>
      </AntHeader>
    </>
  );
}
