'use client';

import React from 'react';
import { Layout, Row, Col, Typography, Divider } from 'antd';
import Link from 'next/link';

const { Footer: AntFooter } = Layout;
const { Title, Text } = Typography;

/**
 * 页脚组件 - 仿照参考网站设计
 */
export default function Footer() {
  // 服务指南链接
  const serviceGuides = [
    { name: '服务条款', link: '/front/service/terms' },
    { name: '工艺介绍', link: '/front/service/process' },
    { name: '货期及验货标准', link: '/front/service/delivery' },
    { name: '下单注意事项', link: '/front/service/order-notes' },
    { name: '常见问题', link: '/front/service/faq' },
  ];

  // 支付方式链接
  const paymentMethods = [
    { name: '在线支付', link: '/front/payment/online' },
    { name: '银行汇款', link: '/front/payment/bank' },
    { name: '发票说明', link: '/front/payment/invoice' },
  ];

  // 配送方式链接
  const deliveryMethods = [
    { name: '配送范围及时间', link: '/front/delivery/scope' },
    { name: '产品签收与验货', link: '/front/delivery/inspection' },
    { name: '配送费用', link: '/front/delivery/cost' },
  ];

  // 售后服务链接
  const afterSales = [
    { name: '售后服务说明', link: '/front/service/after-sales' },
    { name: '返工退换货', link: '/front/service/return' },
    { name: '退款说明', link: '/front/service/refund' },
  ];

  // 关于我们链接
  const aboutUs = [
    { name: '公司简介', link: '/front/about/company' },
    { name: '人才招聘', link: '/front/about/careers' },
    { name: '联系我们', link: '/front/about/contact' },
  ];

  return (
    <AntFooter style={{ backgroundColor: '#333', color: '#ccc', marginTop: '20px' }}>
      <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '20px' }}>
        {/* 主要内容区域 */}
        <Row gutter={[30, 20]} style={{ marginBottom: '20px' }}>
          {/* 买家指南 */}
          <Col span={4}>
            <Title level={5} style={{ color: '#fff', fontSize: '14px', marginBottom: '10px' }}>
              买家指南
            </Title>
            <ul style={{ listStyle: 'none', padding: 0, margin: 0 }}>
              {serviceGuides.map((item, index) => (
                <li key={index} style={{ marginBottom: '5px' }}>
                  <Link href={item.link}>
                    <Text style={{ color: '#ccc', fontSize: '12px' }}>
                      {item.name}
                    </Text>
                  </Link>
                </li>
              ))}
            </ul>
          </Col>

          {/* 支付方式 */}
          <Col span={4}>
            <Title level={5} style={{ color: '#fff', fontSize: '14px', marginBottom: '10px' }}>
              支付方式
            </Title>
            <ul style={{ listStyle: 'none', padding: 0, margin: 0 }}>
              {paymentMethods.map((item, index) => (
                <li key={index} style={{ marginBottom: '5px' }}>
                  <Link href={item.link}>
                    <Text style={{ color: '#ccc', fontSize: '12px' }}>
                      {item.name}
                    </Text>
                  </Link>
                </li>
              ))}
            </ul>
          </Col>

          {/* 配送方式 */}
          <Col span={4}>
            <Title level={5} style={{ color: '#fff', fontSize: '14px', marginBottom: '10px' }}>
              配送方式
            </Title>
            <ul style={{ listStyle: 'none', padding: 0, margin: 0 }}>
              {deliveryMethods.map((item, index) => (
                <li key={index} style={{ marginBottom: '5px' }}>
                  <Link href={item.link}>
                    <Text style={{ color: '#ccc', fontSize: '12px' }}>
                      {item.name}
                    </Text>
                  </Link>
                </li>
              ))}
            </ul>
          </Col>

          {/* 售后服务 */}
          <Col span={4}>
            <Title level={5} style={{ color: '#fff', fontSize: '14px', marginBottom: '10px' }}>
              售后服务
            </Title>
            <ul style={{ listStyle: 'none', padding: 0, margin: 0 }}>
              {afterSales.map((item, index) => (
                <li key={index} style={{ marginBottom: '5px' }}>
                  <Link href={item.link}>
                    <Text style={{ color: '#ccc', fontSize: '12px' }}>
                      {item.name}
                    </Text>
                  </Link>
                </li>
              ))}
            </ul>
          </Col>

          {/* 关于艺创 */}
          <Col span={4}>
            <Title level={5} style={{ color: '#fff', fontSize: '14px', marginBottom: '10px' }}>
              关于艺创
            </Title>
            <ul style={{ listStyle: 'none', padding: 0, margin: 0 }}>
              {aboutUs.map((item, index) => (
                <li key={index} style={{ marginBottom: '5px' }}>
                  <Link href={item.link}>
                    <Text style={{ color: '#ccc', fontSize: '12px' }}>
                      {item.name}
                    </Text>
                  </Link>
                </li>
              ))}
            </ul>
          </Col>
        </Row>

        {/* 分隔线 */}
        <Divider style={{ backgroundColor: '#555', margin: '20px 0' }} />

        {/* 底部版权信息 */}
        <Row justify="space-between" align="middle" style={{ fontSize: '12px' }}>
          <Col>
            <div>
              <Text style={{ color: '#ccc', marginBottom: '5px', display: 'block' }}>
                版权所有 © 2024 艺创包装 |
                <Link href="http://www.beian.miit.gov.cn" style={{ marginLeft: '5px' }}>
                  <Text style={{ color: '#ccc' }}>
                    豫ICP备20017381号-1
                  </Text>
                </Link>
              </Text>
              <Text style={{ color: '#999' }}>
                本网站LOGO和图片已经申请保护，不经授权不得使用
              </Text>
            </div>
          </Col>
          <Col>
            <div>
              <Text style={{ color: '#ccc', marginBottom: '5px', display: 'block' }}>
                有任何问题请联系我们在线客服
              </Text>
              <div>
                <Link href="/admin" style={{ marginRight: '10px' }}>
                  <Text style={{ color: '#ccc' }}>
                    后台登录
                  </Text>
                </Link>
                <Text style={{ color: '#666' }}>|</Text>
                <Link href="/front/sitemap" style={{ marginLeft: '10px' }}>
                  <Text style={{ color: '#ccc' }}>
                    网站地图
                  </Text>
                </Link>
              </div>
            </div>
          </Col>
        </Row>
      </div>
    </AntFooter>
  );
}
